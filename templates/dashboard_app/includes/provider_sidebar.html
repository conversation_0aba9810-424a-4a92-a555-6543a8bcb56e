<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_dashboard' %} active{% endif %}" href="{% url 'dashboard_app:provider_dashboard' %}">
        <i class="fas fa-tachometer-alt me-2"></i>
        Dashboard
    </a>
</li>

<!-- Venue Creation/Management Section -->
{% if not has_venue %}
<li class="nav-item">
    <a class="nav-link venue-creation-link" href="{% url 'venues_app:venue_create' %}">
        <i class="fas fa-plus-circle me-2 text-success"></i>
        Create Venue
        <span class="badge bg-success ms-2">Start Here</span>
    </a>
</li>
{% else %}
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_venues' %} active{% endif %}" href="{% url 'venues_app:provider_venues' %}">
        <i class="fas fa-building me-2"></i>
        My Venue
        {% if venue %}
            {% if venue.approval_status == 'approved' %}
                <span class="badge bg-success ms-2">
                    <i class="fas fa-check"></i>
                </span>
            {% elif venue.approval_status == 'pending' %}
                <span class="badge bg-warning ms-2">
                    <i class="fas fa-clock"></i>
                </span>
            {% elif venue.approval_status == 'rejected' %}
                <span class="badge bg-danger ms-2">
                    <i class="fas fa-times"></i>
                </span>
            {% else %}
                <span class="badge bg-secondary ms-2">
                    <i class="fas fa-edit"></i>
                </span>
            {% endif %}
        {% endif %}
    </a>
</li>
{% endif %}

<!-- Booking Management -->
<li class="nav-item">
    {% if has_venue %}
        <a class="nav-link{% if request.resolver_match.url_name == 'provider_todays_bookings' %} active{% endif %}" href="{% url 'dashboard_app:provider_todays_bookings' %}">
            <i class="fas fa-calendar-day me-2"></i>
            Today's Bookings
        </a>
    {% else %}
        <a class="nav-link locked-item" href="{% url 'dashboard_app:provider_todays_bookings' %}">
            <i class="fas fa-calendar-day me-2"></i>
            Today's Bookings
            <span class="badge bg-secondary ms-2">
                <i class="fas fa-lock"></i>
            </span>
        </a>
    {% endif %}
</li>

<!-- Analytics & Reports -->
<li class="nav-item">
    {% if has_venue %}
        <a class="nav-link{% if request.resolver_match.url_name == 'provider_earnings_reports' %} active{% endif %}" href="{% url 'dashboard_app:provider_earnings_reports' %}">
            <i class="fas fa-chart-line me-2"></i>
            Earnings Reports
        </a>
    {% else %}
        <a class="nav-link locked-item" href="{% url 'dashboard_app:provider_earnings_reports' %}">
            <i class="fas fa-chart-line me-2"></i>
            Earnings Reports
            <span class="badge bg-secondary ms-2">
                <i class="fas fa-lock"></i>
            </span>
        </a>
    {% endif %}
</li>

<li class="nav-item">
    {% if has_venue %}
        <a class="nav-link{% if request.resolver_match.url_name == 'provider_service_performance' %} active{% endif %}" href="{% url 'dashboard_app:provider_service_performance' %}">
            <i class="fas fa-chart-bar me-2"></i>
            Service Performance
        </a>
    {% else %}
        <a class="nav-link locked-item" href="{% url 'dashboard_app:provider_service_performance' %}">
            <i class="fas fa-chart-bar me-2"></i>
            Service Performance
            <span class="badge bg-secondary ms-2">
                <i class="fas fa-lock"></i>
            </span>
        </a>
    {% endif %}
</li>

<!-- Business Management -->
<li class="nav-item">
    {% if has_venue %}
        <a class="nav-link{% if 'discount' in request.resolver_match.url_name %} active{% endif %}" href="{% url 'discount_app:provider_discount_list' %}">
            <i class="fas fa-tags me-2"></i>
            Discounts
        </a>
    {% else %}
        <a class="nav-link locked-item" href="{% url 'discount_app:provider_discount_list' %}">
            <i class="fas fa-tags me-2"></i>
            Discounts
            <span class="badge bg-secondary ms-2">
                <i class="fas fa-lock"></i>
            </span>
        </a>
    {% endif %}
</li>

<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'provider_team_management' %} active{% endif %}" href="{% url 'dashboard_app:provider_team_management' %}">
        <i class="fas fa-users me-2"></i>
        Team Management
    </a>
</li>

<!-- Profile Management -->
<li class="nav-item">
    <a class="nav-link{% if request.resolver_match.url_name == 'service_provider_profile' or request.resolver_match.url_name == 'service_provider_profile_edit' %} active{% endif %}" href="{% url 'accounts_app:service_provider_profile' %}">
        <i class="fas fa-user-edit me-2"></i>
        Profile Management
    </a>
</li>

<!-- Additional Styling for Venue Creation Link -->
<style>
.venue-creation-link {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-left: 4px solid #28a745;
    font-weight: 600;
    margin: 0.5rem 0;
    border-radius: 0.5rem;
}

.venue-creation-link:hover {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.1) 100%);
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.nav-link .badge {
    font-size: 0.7rem;
}

/* Locked item styling */
.locked-item {
    opacity: 0.7;
    color: #6b7280;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
}

.locked-item:hover {
    opacity: 0.8;
    background: #f3f4f6;
    color: #374151;
    border-color: #d1d5db;
}
</style>


