{% extends 'dashboard_app/base_dashboard.html' %}

{% block title %}Manage Services - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}Manage Services{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_actions %}
<div class="btn-group me-2">
    <a href="{% url 'venues_app:service_create' %}" class="btn btn-sm btn-outline-success">
        <i class="fas fa-plus"></i> Add Service
    </a>
    <a href="{% url 'venues_app:provider_venues' %}" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-building"></i> My Venue
    </a>
</div>
{% endblock %}

{% block dashboard_content %}
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_app:provider_dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'venues_app:provider_venues' %}">My Venue</a></li>
        <li class="breadcrumb-item active" aria-current="page">Manage Services</li>
    </ol>
</nav>

<!-- Services Section -->
<section class="services-section">
    <div class="services-container">
        <!-- Services Header -->
        <div class="services-header">
            <div class="content">
                <div>
                    <h1 class="services-title">Manage Services</h1>
                    <p class="services-subtitle">Create and manage services for {{ venue.venue_name }}</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'venues_app:service_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Service
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-card stat-active">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-value">{{ active_services_count }}</div>
                <div class="stat-label">Active Services</div>
            </div>
            <div class="stat-card stat-featured">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-value">{{ featured_services_count }}</div>
                <div class="stat-label">Featured Services</div>
            </div>
            <div class="stat-card stat-categories">
                <div class="stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-value">{{ categories_count }}</div>
                <div class="stat-label">Categories</div>
            </div>
        </div>

        <!-- Services Controls -->
        <div class="services-controls">
            <div class="filter-section">
                <div class="filter-group">
                    <label class="filter-label">Status:</label>
                    <select class="filter-select" id="status-filter">
                        <option value="all">All Services</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Category:</label>
                    <select class="filter-select" id="category-filter">
                        <option value="all">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="search-group">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" id="service-search" placeholder="Search services...">
                </div>
            </div>
            <div class="view-toggle">
                <button class="btn btn-outline-primary active" data-view="grid">
                    <i class="fas fa-th"></i> Grid
                </button>
                <button class="btn btn-outline-primary" data-view="list">
                    <i class="fas fa-list"></i> List
                </button>
            </div>
        </div>

        <!-- Services Grid -->
        <div class="services-grid" id="services-container">
            {% if services %}
                {% for service in services %}
                <div class="service-card" data-status="{{ service.is_active|yesno:'active,inactive' }}" data-category="{{ service.category.id }}">
                    <div class="service-header">
                        <div class="service-image">
                            {% if service.image %}
                                <img src="{{ service.image.url }}" alt="{{ service.service_title }}">
                            {% else %}
                                <div class="service-image-placeholder">
                                    <i class="fas fa-spa"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="service-status">
                            {% if service.is_active %}
                                <span class="status-badge active">Active</span>
                            {% else %}
                                <span class="status-badge inactive">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="service-content">
                        <h3 class="service-title">{{ service.service_title }}</h3>
                        <p class="service-description">{{ service.description|truncatechars:100 }}</p>
                        <div class="service-meta">
                            <div class="service-price">
                                <span class="price-label">Price:</span>
                                <span class="price-value">${{ service.price_min }}{% if service.price_max and service.price_max != service.price_min %} - ${{ service.price_max }}{% endif %}</span>
                            </div>
                            <div class="service-duration">
                                <span class="duration-label">Duration:</span>
                                <span class="duration-value">{{ service.duration_minutes }} min</span>
                            </div>
                        </div>
                        <div class="service-category">
                            <span class="category-badge">{{ service.category.name }}</span>
                        </div>
                    </div>
                    <div class="service-actions">
                        <a href="{% url 'venues_app:service_edit' service.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ service.get_absolute_url }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteService({{ service.id }})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-services">
                    <div class="no-services-content">
                        <i class="fas fa-spa fa-4x text-muted mb-3"></i>
                        <h3>No Services Yet</h3>
                        <p class="text-muted">Start by adding your first service to attract customers.</p>
                        <a href="{% url 'venues_app:service_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Your First Service
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Delete Service Modal -->
<div class="modal fade" id="deleteServiceModal" tabindex="-1" aria-labelledby="deleteServiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteServiceModalLabel">Delete Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this service? This action cannot be undone.</p>
                <p class="text-muted small">Any existing bookings for this service will be affected.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete Service</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Professional sidebar styling with high specificity */
.dashboard-wrapper .dashboard-sidebar {
    width: 280px !important;
    min-width: 280px !important;
}

/* Remove icons and enhance text-only navigation */
.dashboard-wrapper .dashboard-sidebar .nav-link i {
    display: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link {
    font-weight: 500 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    margin: 0.125rem 1rem !important;
    border: 1px solid transparent !important;
    transition: all 0.2s ease !important;
    text-align: left !important;
}

/* Remove "Create Venue" special background styling */
.dashboard-wrapper .dashboard-sidebar .venue-creation-link {
    background: transparent !important;
    border-left: none !important;
    font-weight: 500 !important;
    margin: 0.125rem 1rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid transparent !important;
}

.dashboard-wrapper .dashboard-sidebar .venue-creation-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
    transform: none !important;
    transition: all 0.2s ease !important;
}

/* Remove dashboard button special background */
.dashboard-wrapper .dashboard-sidebar .nav-link.active {
    background: transparent !important;
    color: var(--cw-brand-primary) !important;
    border: 1px solid var(--cw-brand-accent) !important;
    font-weight: 600 !important;
    box-shadow: none !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link:hover {
    background: #f1f5f9 !important;
    color: var(--cw-brand-primary) !important;
    border-color: var(--cw-brand-accent) !important;
}

/* Keep badges and status icons visible */
.dashboard-wrapper .dashboard-sidebar .nav-link .badge {
    font-size: 0.7rem !important;
    display: inline-block !important;
}

.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-check,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-clock,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-times,
.dashboard-wrapper .dashboard-sidebar .nav-link i.fa-edit {
    display: inline !important;
    opacity: 0.6 !important;
}

/* Adjust main content to accommodate wider sidebar */
.dashboard-wrapper .col-md-9.col-lg-10 {
    flex: 0 0 calc(100% - 280px) !important;
    max-width: calc(100% - 280px) !important;
}

/* Service Management Enhanced Styling */
.services-section {
    background: var(--cw-gradient-hero);
    min-height: 100vh;
    padding: 2rem 0;
}

.services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.services-header {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--cw-shadow-md);
    border: 1px solid var(--cw-brand-accent);
}

.services-header .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.services-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin: 0;
    font-family: var(--cw-font-heading);
}

.services-subtitle {
    color: var(--cw-neutral-600);
    margin: 0.5rem 0 0 0;
    font-size: 1.125rem;
}

/* Enhanced Quick Stats with Better Visual Hierarchy */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    border: 2px solid var(--cw-brand-accent);
    box-shadow: var(--cw-shadow-sm);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--cw-brand-primary);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--cw-shadow-lg);
    border-color: var(--cw-brand-primary);
}

.stat-card.stat-active::before {
    background: #10b981;
}

.stat-card.stat-featured::before {
    background: #f59e0b;
}

.stat-card.stat-categories::before {
    background: #8b5cf6;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
    font-family: var(--cw-font-heading);
    line-height: 1;
}

.stat-label {
    color: var(--cw-neutral-600);
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
    background: var(--cw-accent-light);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--cw-brand-primary);
    font-size: 1.125rem;
    opacity: 0.3;
}

/* Enhanced Filters and Controls */
.services-controls {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--cw-shadow-md);
    border: 1px solid var(--cw-brand-accent);
}

.filter-section {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 600;
    color: var(--cw-brand-primary);
    white-space: nowrap;
    font-size: 0.875rem;
}

.filter-select, .search-input {
    border: 2px solid var(--cw-brand-accent);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    background: white;
    color: var(--cw-neutral-800);
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.filter-select:focus, .search-input:focus {
    border-color: var(--cw-brand-primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
}

.search-group {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-input {
    width: 100%;
    padding-left: 2.5rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--cw-neutral-600);
}

/* Enhanced View Toggle */
.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-toggle .btn {
    border: 2px solid var(--cw-brand-accent);
    background: white;
    color: var(--cw-brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.view-toggle .btn.active,
.view-toggle .btn:hover {
    background: var(--cw-brand-primary);
    color: white;
    border-color: var(--cw-brand-primary);
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--cw-shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--cw-brand-accent);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--cw-shadow-lg);
    border-color: var(--cw-brand-primary);
}

.service-header {
    position: relative;
}

.service-image {
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-image-placeholder {
    width: 100%;
    height: 100%;
    background: var(--cw-accent-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--cw-brand-primary);
    font-size: 3rem;
}

.service-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #10b981;
    color: white;
}

.status-badge.inactive {
    background: #6b7280;
    color: white;
}

.service-content {
    padding: 1.5rem;
}

.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--cw-brand-primary);
    margin-bottom: 0.5rem;
}

.service-description {
    color: var(--cw-neutral-600);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.service-price, .service-duration {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.price-label, .duration-label {
    font-size: 0.75rem;
    color: var(--cw-neutral-500);
    text-transform: uppercase;
    font-weight: 500;
}

.price-value, .duration-value {
    font-weight: 600;
    color: var(--cw-brand-primary);
}

.service-category {
    margin-bottom: 1rem;
}

.category-badge {
    background: var(--cw-accent-light);
    color: var(--cw-brand-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.service-actions {
    padding: 1rem 1.5rem;
    background: var(--cw-accent-light);
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.service-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* No Services State */
.no-services {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
}

.no-services-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-services-content h3 {
    color: var(--cw-brand-primary);
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .services-header .content {
        flex-direction: column;
        text-align: center;
    }

    .services-title {
        font-size: 1.75rem;
    }

    .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .stat-value {
        font-size: 2rem;
    }

    .filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-group {
        min-width: auto;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Service management JavaScript
let currentServiceId = null;

function deleteService(serviceId) {
    currentServiceId = serviceId;
    const modal = new bootstrap.Modal(document.getElementById('deleteServiceModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (currentServiceId) {
        window.location.href = `/venues/service/${currentServiceId}/delete/`;
    }
});

// Filter functionality
document.getElementById('status-filter').addEventListener('change', filterServices);
document.getElementById('category-filter').addEventListener('change', filterServices);
document.getElementById('service-search').addEventListener('input', filterServices);

function filterServices() {
    const statusFilter = document.getElementById('status-filter').value;
    const categoryFilter = document.getElementById('category-filter').value;
    const searchTerm = document.getElementById('service-search').value.toLowerCase();
    
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        const status = card.dataset.status;
        const category = card.dataset.category;
        const title = card.querySelector('.service-title').textContent.toLowerCase();
        const description = card.querySelector('.service-description').textContent.toLowerCase();
        
        const statusMatch = statusFilter === 'all' || status === statusFilter;
        const categoryMatch = categoryFilter === 'all' || category === categoryFilter;
        const searchMatch = title.includes(searchTerm) || description.includes(searchTerm);
        
        if (statusMatch && categoryMatch && searchMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// View toggle functionality
document.querySelectorAll('.view-toggle .btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.view-toggle .btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        
        const view = this.dataset.view;
        const container = document.getElementById('services-container');
        
        if (view === 'list') {
            container.style.gridTemplateColumns = '1fr';
        } else {
            container.style.gridTemplateColumns = 'repeat(auto-fill, minmax(300px, 1fr))';
        }
    });
});
</script>
{% endblock %}

